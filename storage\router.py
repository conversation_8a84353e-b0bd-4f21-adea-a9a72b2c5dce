from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter()

class StorageStatus(BaseModel):
    temperature: float
    humidity: Optional[float]
    door_open: bool

@router.get("/status", response_model=StorageStatus)
async def get_storage_status():
    """
    Get current storage status including temperature and door state
    """
    try:
        # Here you would implement actual hardware communication
        return StorageStatus(
            temperature=20.5,
            door_open=False
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/door/toggle")
async def toggle_door():
    """
    Toggle storage door state
    """
    try:
        # Here you would implement actual hardware communication
        return {"status": "success", "message": "Door state toggled"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 